use std::env;
use std::path::PathBuf;

fn main() {
    // Get the directory where the library is located
    let lib_dir = PathBuf::from(env::var("CARGO_MANIFEST_DIR").unwrap()).join("lib");

    // Tell cargo to look for shared libraries in the lib directory
    println!("cargo:rustc-link-search=native={}", lib_dir.display());

    // Link to the Go library
    println!("cargo:rustc-link-lib=dylib=token_balance");

    // Tell cargo to invalidate the built crate whenever the library changes
    println!("cargo:rerun-if-changed=lib/libtoken_balance.dylib");

    // Set the library path for runtime
    if cfg!(target_os = "macos") {
        println!("cargo:rustc-env=DYLD_LIBRARY_PATH={}", lib_dir.display());
    } else if cfg!(target_os = "linux") {
        println!("cargo:rustc-env=LD_LIBRARY_PATH={}", lib_dir.display());
    }
}
