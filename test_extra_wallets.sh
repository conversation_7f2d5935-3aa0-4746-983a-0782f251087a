#!/bin/bash

# Test script for extra_wallets functionality
# This script tests the enhanced endpoint with extra_wallets parameter

set -e

echo "Testing extra_wallets functionality..."

# Server URL
SERVER_URL="http://localhost:8088"

# Test token (USDC)
TOKEN="EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"

# Test extra wallets
EXTRA_WALLETS='["9WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9zYtAWWM", "5Q544fKrFoe6tsEbD7S8EmxGTJYAKtTVhAW5Q5pge4j1"]'

echo "1. Testing health check..."
curl -s "$SERVER_URL/health" || {
    echo "Error: Server is not running. Please start the server with: cargo run --bin token_analysis_server"
    exit 1
}
echo " ✓ Server is running"

echo ""
echo "2. Testing enhanced endpoint without extra_wallets..."
RESPONSE1=$(curl -s -X POST "$SERVER_URL/analyze/suspect_wallets_balances_enhanced" \
    -H "Content-Type: application/json" \
    -d "{\"token\": \"$TOKEN\", \"from_slot\": 346464000}")

echo "Response (without extra_wallets):"
echo "$RESPONSE1" | jq '.' 2>/dev/null || echo "$RESPONSE1"

echo ""
echo "3. Testing enhanced endpoint with extra_wallets..."
RESPONSE2=$(curl -s -X POST "$SERVER_URL/analyze/suspect_wallets_balances_enhanced" \
    -H "Content-Type: application/json" \
    -d "{\"token\": \"$TOKEN\", \"from_slot\": 346464000, \"extra_wallets\": $EXTRA_WALLETS}")

echo "Response (with extra_wallets):"
echo "$RESPONSE2" | jq '.' 2>/dev/null || echo "$RESPONSE2"

echo ""
echo "4. Checking if extra_balance field is present in balance_points..."
if echo "$RESPONSE2" | grep -q "extra_balance"; then
    echo " ✓ extra_balance field found in response"
else
    echo " ✗ extra_balance field NOT found in response"
fi

echo ""
echo "5. Checking if extra_wallets are included in response..."
if echo "$RESPONSE2" | grep -q "extra_wallets"; then
    echo " ✓ extra_wallets field found in response"
else
    echo " ✗ extra_wallets field NOT found in response"
fi

echo ""
echo "Test completed!"
echo ""
echo "To run this test:"
echo "1. Start the server: cargo run --bin token_analysis_server"
echo "2. Run this script: ./test_extra_wallets.sh"
echo ""
echo "Note: This test requires:"
echo "- A running database with proper configuration"
echo "- The Go FFI library to be built and available"
echo "- Network access to fetch blockchain data"
