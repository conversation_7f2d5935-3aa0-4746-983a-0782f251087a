package main

/*
#include <stdlib.h>
#include <string.h>
*/
import "C"

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"os"
	"strings"
	"sync"
	"unsafe"

	"github.com/kryptogo/kg-solana-data/data-writer/db"
	"github.com/kryptogo/kg-solana-data/data-writer/logger"
	"github.com/kryptogo/kg-solana-data/data-writer/token_balance"
)

var (
	initOnce sync.Once
	initErr  error
)

// FFITokenBalanceConfig represents the configuration for FFI calls
type FFITokenBalanceConfig struct {
	FromSlot     uint64   `json:"from_slot"`
	RPCURL       string   `json:"rpc_url"`
	ExtraWallets []string `json:"extra_wallets,omitempty"`
}

// FFITokenBalanceResult represents the result returned via FFI
type FFITokenBalanceResult struct {
	BalancePoints       []token_balance.BalancePoint `json:"balance_points"`
	TotalCurrentBalance int64                        `json:"total_current_balance"`
	NumIntervals        uint64                       `json:"num_intervals"`
	Error               string                       `json:"error,omitempty"`
}

// initializeOnce initializes the logger and database connections once
func initializeOnce() {
	initOnce.Do(func() {
		ctx := context.Background()

		// Initialize logger
		if err := logger.Initialize(ctx, "token-balance-ffi"); err != nil {
			initErr = fmt.Errorf("failed to initialize logger: %v", err)
			return
		}

		// Initialize database
		if err := db.Initialize(); err != nil {
			initErr = fmt.Errorf("failed to initialize database: %v", err)
			return
		}
	})
}

//export InitializeTokenBalance
func InitializeTokenBalance() *C.char {
	initializeOnce()
	if initErr != nil {
		result := map[string]string{"error": initErr.Error()}
		jsonBytes, _ := json.Marshal(result)
		return C.CString(string(jsonBytes))
	}

	result := map[string]string{"status": "success"}
	jsonBytes, _ := json.Marshal(result)
	return C.CString(string(jsonBytes))
}

//export CalculateTokenBalanceHistoryFFI
func CalculateTokenBalanceHistoryFFI(tokenMint *C.char, walletsJSON *C.char, configJSON *C.char) *C.char {
	// Ensure initialization
	initializeOnce()
	if initErr != nil {
		result := FFITokenBalanceResult{Error: initErr.Error()}
		jsonBytes, _ := json.Marshal(result)
		return C.CString(string(jsonBytes))
	}

	// Convert C strings to Go strings
	goTokenMint := C.GoString(tokenMint)
	goWalletsJSON := C.GoString(walletsJSON)
	goConfigJSON := C.GoString(configJSON)

	// Parse wallets JSON array
	var wallets []string
	if err := json.Unmarshal([]byte(goWalletsJSON), &wallets); err != nil {
		result := FFITokenBalanceResult{Error: fmt.Sprintf("failed to parse wallets JSON: %v", err)}
		jsonBytes, _ := json.Marshal(result)
		return C.CString(string(jsonBytes))
	}

	// Parse config JSON
	var config FFITokenBalanceConfig
	if err := json.Unmarshal([]byte(goConfigJSON), &config); err != nil {
		result := FFITokenBalanceResult{Error: fmt.Sprintf("failed to parse config JSON: %v", err)}
		jsonBytes, _ := json.Marshal(result)
		return C.CString(string(jsonBytes))
	}

	// Convert to internal config format
	internalConfig := token_balance.TokenBalanceConfig{
		FromSlot:         config.FromSlot,
		RPCURL:           config.RPCURL,
		CommitmentLevel:  "", // Use default (Finalized)
		DefaultRPCURL:    "https://solana-mainnet.g.alchemy.com/v2/********************************",
		ContinueOnErrors: true, // Continue on errors for FFI
		ExtraWallets:     config.ExtraWallets,
	}

	// Multiple ways to ensure debug output is visible
	fmt.Printf("FFI: Calculating balance history for token %s from slot %d for wallets: %v\n", goTokenMint, config.FromSlot, wallets)

	// Also use log package which might be more reliable in shared library context
	log.Printf("FFI: Starting calculation for token %s with %d wallets\n", goTokenMint, len(wallets))

	// Write to stderr as well (sometimes more reliable than stdout)
	fmt.Fprintf(os.Stderr, "FFI: Debug - Processing token %s\n", goTokenMint)
	// Call the actual function
	ctx := context.Background()
	os.Stdout.Sync()
	os.Stderr.Sync()

	result, err := token_balance.CalculateTokenBalanceHistory(ctx, goTokenMint, wallets, internalConfig)
	if err != nil {
		ffiResult := FFITokenBalanceResult{Error: err.Error()}
		jsonBytes, _ := json.Marshal(ffiResult)
		return C.CString(string(jsonBytes))
	}

	// Convert result to FFI format
	ffiResult := FFITokenBalanceResult{
		BalancePoints:       result.BalancePoints,
		TotalCurrentBalance: result.TotalCurrentBalance,
		NumIntervals:        result.NumIntervals,
	}

	// Marshal to JSON
	jsonBytes, err := json.Marshal(ffiResult)
	if err != nil {
		errorResult := FFITokenBalanceResult{Error: fmt.Sprintf("failed to marshal result: %v", err)}
		errorBytes, _ := json.Marshal(errorResult)
		return C.CString(string(errorBytes))
	}

	return C.CString(string(jsonBytes))
}

//export FreeString
func FreeString(str *C.char) {
	C.free(unsafe.Pointer(str))
}

//export CleanupTokenBalance
func CleanupTokenBalance() {
	if err := logger.Close(); err != nil {
		fmt.Printf("Error closing logger: %v\n", err)
	}
	if err := db.Close(); err != nil {
		fmt.Printf("Error closing database: %v\n", err)
	}
}

//export CalculateTokenBalanceHistorySimple
func CalculateTokenBalanceHistorySimple(tokenMint *C.char, walletsCSV *C.char, fromSlot C.ulonglong, rpcURL *C.char) *C.char {
	// Convert inputs
	goTokenMint := C.GoString(tokenMint)
	goWalletsCSV := C.GoString(walletsCSV)
	goRPCURL := C.GoString(rpcURL)

	// Parse wallets from CSV
	wallets := strings.Split(goWalletsCSV, ",")
	for i, wallet := range wallets {
		wallets[i] = strings.TrimSpace(wallet)
	}

	// Create config
	config := FFITokenBalanceConfig{
		FromSlot: uint64(fromSlot),
		RPCURL:   goRPCURL,
	}

	// Convert to JSON and call main function
	walletsJSON, _ := json.Marshal(wallets)
	configJSON, _ := json.Marshal(config)

	return CalculateTokenBalanceHistoryFFI(
		C.CString(goTokenMint),
		C.CString(string(walletsJSON)),
		C.CString(string(configJSON)),
	)
}

func main() {
	// Empty main function for shared library build
}
