package token_balance

import (
	"encoding/json"
	"testing"
)

func TestBalancePointSerialization(t *testing.T) {
	// Test that BalancePoint properly serializes with extra_balance field
	bp := BalancePoint{
		Timestamp:    1678901234,
		Balance:      1000000,
		SolBalance:   500000,
		ExtraBalance: 250000,
	}

	// Serialize to JSON
	jsonData, err := json.Marshal(bp)
	if err != nil {
		t.Fatalf("Failed to marshal BalancePoint: %v", err)
	}

	jsonStr := string(jsonData)
	t.Logf("Serialized BalancePoint: %s", jsonStr)

	// Check that all fields are present
	if !contains(jsonStr, "timestamp") {
		t.<PERSON>r("timestamp field missing from JSON")
	}
	if !contains(jsonStr, "balance") {
		t.<PERSON>r("balance field missing from JSON")
	}
	if !contains(jsonStr, "sol_balance") {
		t.<PERSON>rror("sol_balance field missing from JSON")
	}
	if !contains(jsonStr, "extra_balance") {
		t.<PERSON><PERSON><PERSON>("extra_balance field missing from JSON")
	}
	if !contains(jsonStr, "250000") {
		t.<PERSON>("extra_balance value missing from JSON")
	}

	// Test deserialization
	var deserializedBP BalancePoint
	err = json.Unmarshal(jsonData, &deserializedBP)
	if err != nil {
		t.Fatalf("Failed to unmarshal BalancePoint: %v", err)
	}

	// Verify all fields
	if deserializedBP.Timestamp != bp.Timestamp {
		t.Errorf("Timestamp mismatch: expected %d, got %d", bp.Timestamp, deserializedBP.Timestamp)
	}
	if deserializedBP.Balance != bp.Balance {
		t.Errorf("Balance mismatch: expected %d, got %d", bp.Balance, deserializedBP.Balance)
	}
	if deserializedBP.SolBalance != bp.SolBalance {
		t.Errorf("SolBalance mismatch: expected %d, got %d", bp.SolBalance, deserializedBP.SolBalance)
	}
	if deserializedBP.ExtraBalance != bp.ExtraBalance {
		t.Errorf("ExtraBalance mismatch: expected %d, got %d", bp.ExtraBalance, deserializedBP.ExtraBalance)
	}
}

func TestTokenBalanceConfigWithExtraWallets(t *testing.T) {
	// Test TokenBalanceConfig with ExtraWallets field
	config := TokenBalanceConfig{
		FromSlot:         250000000,
		RPCURL:           "https://api.mainnet-beta.solana.com",
		CommitmentLevel:  "",
		DefaultRPCURL:    "",
		ContinueOnErrors: true,
		ExtraWallets: []string{
			"9WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9zYtAWWM",
			"5Q544fKrFoe6tsEbD7S8EmxGTJYAKtTVhAW5Q5pge4j1",
		},
	}

	// Serialize to JSON
	jsonData, err := json.Marshal(config)
	if err != nil {
		t.Fatalf("Failed to marshal TokenBalanceConfig: %v", err)
	}

	jsonStr := string(jsonData)
	t.Logf("Serialized TokenBalanceConfig: %s", jsonStr)

	// Check that ExtraWallets field is present
	if !contains(jsonStr, "ExtraWallets") {
		t.Error("ExtraWallets field missing from JSON")
	}
	if !contains(jsonStr, "9WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9zYtAWWM") {
		t.Error("First extra wallet missing from JSON")
	}
	if !contains(jsonStr, "5Q544fKrFoe6tsEbD7S8EmxGTJYAKtTVhAW5Q5pge4j1") {
		t.Error("Second extra wallet missing from JSON")
	}

	// Test deserialization
	var deserializedConfig TokenBalanceConfig
	err = json.Unmarshal(jsonData, &deserializedConfig)
	if err != nil {
		t.Fatalf("Failed to unmarshal TokenBalanceConfig: %v", err)
	}

	// Verify ExtraWallets field
	if len(deserializedConfig.ExtraWallets) != 2 {
		t.Errorf("ExtraWallets length mismatch: expected 2, got %d", len(deserializedConfig.ExtraWallets))
	}
	if len(deserializedConfig.ExtraWallets) >= 1 && deserializedConfig.ExtraWallets[0] != config.ExtraWallets[0] {
		t.Errorf("First extra wallet mismatch: expected %s, got %s", config.ExtraWallets[0], deserializedConfig.ExtraWallets[0])
	}
	if len(deserializedConfig.ExtraWallets) >= 2 && deserializedConfig.ExtraWallets[1] != config.ExtraWallets[1] {
		t.Errorf("Second extra wallet mismatch: expected %s, got %s", config.ExtraWallets[1], deserializedConfig.ExtraWallets[1])
	}
}

func TestTokenBalanceConfigWithoutExtraWallets(t *testing.T) {
	// Test TokenBalanceConfig without ExtraWallets field
	config := TokenBalanceConfig{
		FromSlot:         250000000,
		RPCURL:           "https://api.mainnet-beta.solana.com",
		CommitmentLevel:  "",
		DefaultRPCURL:    "",
		ContinueOnErrors: true,
		ExtraWallets:     nil, // No extra wallets
	}

	// Serialize to JSON
	jsonData, err := json.Marshal(config)
	if err != nil {
		t.Fatalf("Failed to marshal TokenBalanceConfig: %v", err)
	}

	jsonStr := string(jsonData)
	t.Logf("Serialized TokenBalanceConfig (no extra wallets): %s", jsonStr)

	// Test deserialization
	var deserializedConfig TokenBalanceConfig
	err = json.Unmarshal(jsonData, &deserializedConfig)
	if err != nil {
		t.Fatalf("Failed to unmarshal TokenBalanceConfig: %v", err)
	}

	// Verify ExtraWallets field is nil or empty
	if deserializedConfig.ExtraWallets != nil && len(deserializedConfig.ExtraWallets) > 0 {
		t.Errorf("ExtraWallets should be nil or empty, got %v", deserializedConfig.ExtraWallets)
	}
}

// Helper function to check if a string contains a substring
func contains(s, substr string) bool {
	return len(s) >= len(substr) && (s == substr || len(substr) == 0 || 
		(len(s) > len(substr) && (s[:len(substr)] == substr || s[len(s)-len(substr):] == substr || 
		containsAt(s, substr))))
}

func containsAt(s, substr string) bool {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}
