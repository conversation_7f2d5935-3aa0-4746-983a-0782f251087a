use anyhow::Result;
use axum::{
    http::StatusCode,
    response::IntoResponse,
    routing::{get, post},
    Router,
};
use serde::{Deserialize, Serialize};
use std::net::SocketAddr;
use tokio;
use tower_http::cors::{Any, CorsLayer};

#[derive(Debug, Deserialize)]
struct TokenRequest {
    token: String,
}

#[derive(Debug, Deserialize, Serialize)]
struct BalancePoint {
    timestamp: i64,
    sol_balance: i64,
    extra_balance: i64,
    balance: i64,
}

#[derive(Debug, Deserialize)]
struct TokenBalanceResult {
    #[serde(rename = "BalancePoints")]
    balance_points: Vec<BalancePoint>,
    #[serde(rename = "TotalCurrentBalance")]
    total_current_balance: i64,
    #[serde(rename = "NumIntervals")]
    num_intervals: u64,
}

#[derive(Debug, Deserialize)]
struct TokenBalanceRequest {
    token: String,
    from_slot: Option<u64>,
    rpc_url: Option<String>,
    extra_wallets: Option<Vec<String>>,
}

async fn analyze_token_holders(
    axum::extract::Json(req): axum::extract::Json<TokenRequest>,
) -> impl IntoResponse {
    match solana_bot::token_analysis::analyze_token(&req.token).await {
        Ok(analysis) => {
            let response = serde_json::json!({
                "status": "success",
                "data": {
                    "token": req.token,
                    "analysis": {
                        "liquidity_pools": analysis.liquidity_pools.total_percentage,
                        "cex": analysis.cex.total_percentage,
                        "individual_traders": analysis.individual_traders.total_percentage,
                        "kol_traders": analysis.kol_traders.total_percentage,
                        "suspect_insiders": analysis.suspect_insiders.total_percentage,
                        "others": analysis.others.total_percentage,
                        "suspect_insiders_sol_balance": analysis.suspect_insiders_sol_balance
                    }
                }
            });
            (StatusCode::OK, axum::Json(response)).into_response()
        }
        Err(e) => {
            let error_response = serde_json::json!({
                "status": "error",
                "message": format!("Error analyzing token: {}", e)
            });
            (
                StatusCode::INTERNAL_SERVER_ERROR,
                axum::Json(error_response),
            )
                .into_response()
        }
    }
}

async fn get_suspect_wallets(
    axum::extract::Json(req): axum::extract::Json<TokenRequest>,
) -> impl IntoResponse {
    match solana_bot::token_analysis::get_suspect_wallets(&req.token).await {
        Ok(wallets) => {
            let response = serde_json::json!({
                "status": "success",
                "data": {
                    "token": req.token,
                    "suspect_wallets": wallets.iter().map(|w| w.to_string()).collect::<Vec<String>>()
                }
            });
            (StatusCode::OK, axum::Json(response)).into_response()
        }
        Err(e) => {
            let error_response = serde_json::json!({
                "status": "error",
                "message": format!("Error analyzing token: {}", e)
            });
            (
                StatusCode::INTERNAL_SERVER_ERROR,
                axum::Json(error_response),
            )
                .into_response()
        }
    }
}

// Helper function to call the Go CalculateTokenBalanceHistory function via FFI
async fn call_go_balance_history_ffi(
    token_mint: &str,
    wallets: &[String],
    from_slot: u64,
    rpc_url: Option<&str>,
    extra_wallets: Option<&[String]>,
) -> Result<TokenBalanceResult, anyhow::Error> {
    // Create configuration for the Go function
    let config = solana_bot::token_balance_ffi::TokenBalanceConfig {
        from_slot,
        rpc_url: rpc_url.unwrap_or("").to_string(),
        extra_wallets: extra_wallets.map(|w| w.to_vec()),
    };

    // Call the Go function via FFI
    let result = solana_bot::token_balance_ffi::calculate_token_balance_history(
        token_mint, wallets, config,
    )?;

    // Convert the FFI result to our internal format
    Ok(TokenBalanceResult {
        balance_points: result
            .balance_points
            .into_iter()
            .map(|bp| BalancePoint {
                timestamp: bp.timestamp,
                sol_balance: bp.sol_balance,
                balance: bp.balance,
                extra_balance: bp.extra_balance,
            })
            .collect(),
        total_current_balance: result.total_current_balance,
        num_intervals: result.num_intervals,
    })
}

async fn get_suspect_wallets_balances_enhanced(
    axum::extract::Json(req): axum::extract::Json<TokenBalanceRequest>,
) -> impl IntoResponse {
    // First, get the suspect wallets
    match solana_bot::token_analysis::get_suspect_wallets(&req.token).await {
        Ok(wallets) => {
            if wallets.is_empty() {
                let response = serde_json::json!({
                    "status": "success",
                    "data": {
                        "token": req.token,
                        "suspect_wallets": [],
                        "balance_history": []
                    }
                });
                return (StatusCode::OK, axum::Json(response)).into_response();
            }

            // Convert wallets to strings
            let wallet_strings: Vec<String> = wallets.iter().map(|w| w.to_string()).collect();

            // Set default from_slot if not provided (e.g., 30 days ago in slots)
            let from_slot = req.from_slot.unwrap_or(346464000); // Default starting slot

            // Call the Go function to get balance history using FFI
            match call_go_balance_history_ffi(
                &req.token,
                &wallet_strings,
                from_slot,
                req.rpc_url.as_deref(),
                req.extra_wallets.as_deref(),
            )
            .await
            {
                Ok(balance_result) => {
                    let response = serde_json::json!({
                        "status": "success",
                        "data": {
                            "token": req.token,
                            "suspect_wallets": wallet_strings,
                            "extra_wallets": req.extra_wallets.unwrap_or_default(),
                            "balance_history": {
                                "balance_points": balance_result.balance_points,
                                "total_current_balance": balance_result.total_current_balance,
                                "num_intervals": balance_result.num_intervals,
                                "from_slot": from_slot
                            }
                        }
                    });
                    (StatusCode::OK, axum::Json(response)).into_response()
                }
                Err(e) => {
                    log::error!("Failed to get balance history: {}", e);
                    let error_response = serde_json::json!({
                        "status": "error",
                        "message": format!("Error calculating balance history: {}", e)
                    });
                    (
                        StatusCode::INTERNAL_SERVER_ERROR,
                        axum::Json(error_response),
                    )
                        .into_response()
                }
            }
        }
        Err(e) => {
            let error_response = serde_json::json!({
                "status": "error",
                "message": format!("Error analyzing token: {}", e)
            });
            (
                StatusCode::INTERNAL_SERVER_ERROR,
                axum::Json(error_response),
            )
                .into_response()
        }
    }
}

async fn health_check() -> &'static str {
    "OK"
}

#[tokio::main(flavor = "multi_thread", worker_threads = 4)]
async fn main() -> Result<()> {
    // Initialize logging
    env_logger::init();
    log::info!("Starting token analysis server...");

    // Initialize the Go FFI library
    log::info!("Initializing Go FFI library...");
    if let Err(e) = solana_bot::token_balance_ffi::initialize() {
        log::warn!(
            "Failed to initialize Go FFI library: {}. FFI features will be disabled.",
            e
        );
        log::info!(
            "Note: This is expected if database is not configured or Go library is not available."
        );
    } else {
        log::info!("Go FFI library initialized successfully");
    }

    // Create the router with CORS enabled
    let app = Router::new()
        .route("/analyze", post(analyze_token_holders))
        .route("/analyze/suspect_wallets", post(get_suspect_wallets))
        .route(
            "/analyze/suspect_wallets_balances_enhanced",
            post(get_suspect_wallets_balances_enhanced),
        )
        .route("/health", get(health_check))
        .layer(
            CorsLayer::new()
                .allow_origin(Any)
                .allow_methods(Any)
                .allow_headers(Any),
        );

    // Start the server
    let addr = SocketAddr::from(([0, 0, 0, 0], 8088));
    log::info!("Server listening on {}", addr);

    let listener = tokio::net::TcpListener::bind(addr).await?;

    // Set up cleanup on shutdown
    let result = axum::serve(listener, app).await;

    // Cleanup FFI library
    log::info!("Cleaning up Go FFI library...");
    solana_bot::token_balance_ffi::cleanup();

    result?;
    Ok(())
}
