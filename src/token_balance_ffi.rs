use anyhow::Result;
use serde::{Deserialize, Serialize};
use std::ffi::{CStr, CString};
use std::os::raw::c_char;

#[derive(Debug, Deserialize, Serialize)]
pub struct BalancePoint {
    pub timestamp: i64,
    pub balance: i64,
    pub sol_balance: i64,
    pub extra_balance: i64,
}

#[derive(Debug, Deserialize, Serialize)]
pub struct TokenBalanceConfig {
    pub from_slot: u64,
    pub rpc_url: String,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub extra_wallets: Option<Vec<String>>,
}

#[derive(Debug, Deserialize)]
pub struct TokenBalanceResult {
    pub balance_points: Vec<BalancePoint>,
    pub total_current_balance: i64,
    pub num_intervals: u64,
    pub error: Option<String>,
}

// External C functions from the Go library
extern "C" {
    fn InitializeTokenBalance() -> *mut c_char;
    fn CalculateTokenBalanceHistoryFFI(
        token_mint: *const c_char,
        wallets_json: *const c_char,
        config_json: *const c_char,
    ) -> *mut c_char;
    fn FreeString(s: *mut c_char);
    fn CleanupTokenBalance();
}

/// Initialize the token balance library (database and logger)
pub fn initialize() -> Result<()> {
    unsafe {
        let result_ptr = InitializeTokenBalance();
        let result_str = CStr::from_ptr(result_ptr).to_str()?;
        let result: serde_json::Value = serde_json::from_str(result_str)?;

        // Free the string allocated by Go
        FreeString(result_ptr);

        if let Some(error) = result.get("error") {
            return Err(anyhow::anyhow!("Initialization failed: {}", error));
        }

        Ok(())
    }
}

/// Calculate token balance history using the Go FFI function
pub fn calculate_token_balance_history(
    token_mint: &str,
    wallets: &[String],
    config: TokenBalanceConfig,
) -> Result<TokenBalanceResult> {
    unsafe {
        // Convert inputs to C strings
        let token_mint_c: CString = CString::new(token_mint)?;
        let wallets_json = serde_json::to_string(wallets)?;
        let wallets_json_c = CString::new(wallets_json)?;
        let config_json = serde_json::to_string(&config)?;
        let config_json_c = CString::new(config_json)?;

        // Call the Go function
        let result_ptr = CalculateTokenBalanceHistoryFFI(
            token_mint_c.as_ptr(),
            wallets_json_c.as_ptr(),
            config_json_c.as_ptr(),
        );

        // Convert result back to Rust string
        let result_str = CStr::from_ptr(result_ptr).to_str()?;
        let result: TokenBalanceResult = serde_json::from_str(result_str)?;

        // Free the string allocated by Go
        FreeString(result_ptr);

        // Check for errors in the result
        if let Some(error) = &result.error {
            return Err(anyhow::anyhow!("Go function failed: {}", error));
        }

        Ok(result)
    }
}

/// Cleanup the token balance library
pub fn cleanup() {
    unsafe {
        CleanupTokenBalance();
    }
}

/// RAII wrapper for automatic cleanup
pub struct TokenBalanceLibrary {
    initialized: bool,
}

impl TokenBalanceLibrary {
    pub fn new() -> Result<Self> {
        initialize()?;
        Ok(TokenBalanceLibrary { initialized: true })
    }

    pub fn calculate_balance_history(
        &self,
        token_mint: &str,
        wallets: &[String],
        config: TokenBalanceConfig,
    ) -> Result<TokenBalanceResult> {
        if !self.initialized {
            return Err(anyhow::anyhow!("Library not initialized"));
        }
        calculate_token_balance_history(token_mint, wallets, config)
    }
}

impl Drop for TokenBalanceLibrary {
    fn drop(&mut self) {
        if self.initialized {
            cleanup();
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_initialization() {
        // This test just verifies that the FFI bindings compile and link correctly
        // Actual functionality testing would require a proper database setup
        let result = initialize();

        // We expect this to either succeed or fail gracefully
        match result {
            Ok(_) => {
                cleanup();
                println!("FFI initialization successful");
            }
            Err(e) => {
                println!(
                    "FFI initialization failed (expected in test environment): {}",
                    e
                );
            }
        }
    }

    #[test]
    fn test_library_wrapper() {
        // Test the RAII wrapper
        let _lib = TokenBalanceLibrary::new();
        // Library should be automatically cleaned up when dropped
    }

    #[test]
    fn test_config_serialization_with_extra_wallets() {
        // Test that the TokenBalanceConfig properly serializes extra_wallets
        let config_with_extra = TokenBalanceConfig {
            from_slot: 250000000,
            rpc_url: "https://api.mainnet-beta.solana.com".to_string(),
            extra_wallets: Some(vec![
                "9WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9zYtAWWM".to_string(),
                "5Q544fKrFoe6tsEbD7S8EmxGTJYAKtTVhAW5Q5pge4j1".to_string(),
            ]),
        };

        let json = serde_json::to_string(&config_with_extra).expect("Failed to serialize config");
        println!("Config with extra wallets: {}", json);

        // Verify that extra_wallets is included in the JSON
        assert!(json.contains("extra_wallets"));
        assert!(json.contains("9WzDXwBbmkg8ZTbNMqUxvQRAyrZzDsGYdLVL9zYtAWWM"));

        // Test config without extra wallets
        let config_without_extra = TokenBalanceConfig {
            from_slot: 250000000,
            rpc_url: "https://api.mainnet-beta.solana.com".to_string(),
            extra_wallets: None,
        };

        let json_without =
            serde_json::to_string(&config_without_extra).expect("Failed to serialize config");
        println!("Config without extra wallets: {}", json_without);

        // Verify that extra_wallets is not included when None (due to skip_serializing_if)
        assert!(!json_without.contains("extra_wallets"));
    }

    #[test]
    fn test_balance_point_with_extra_balance() {
        // Test that BalancePoint properly handles extra_balance field
        let balance_point = BalancePoint {
            timestamp: 1678901234,
            balance: 1000000,
            sol_balance: 500000,
            extra_balance: 250000,
        };

        let json = serde_json::to_string(&balance_point).expect("Failed to serialize BalancePoint");
        println!("BalancePoint JSON: {}", json);

        // Verify all fields are present
        assert!(json.contains("timestamp"));
        assert!(json.contains("balance"));
        assert!(json.contains("sol_balance"));
        assert!(json.contains("extra_balance"));
        assert!(json.contains("250000"));

        // Test deserialization
        let deserialized: BalancePoint =
            serde_json::from_str(&json).expect("Failed to deserialize BalancePoint");
        assert_eq!(deserialized.timestamp, 1678901234);
        assert_eq!(deserialized.balance, 1000000);
        assert_eq!(deserialized.sol_balance, 500000);
        assert_eq!(deserialized.extra_balance, 250000);
    }
}
